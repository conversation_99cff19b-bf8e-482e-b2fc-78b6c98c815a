import os
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from utility import get_sell_option_price, get_calender_diff_value, find_closest_option
from VIX_Threshold import get_vix
#import logger_config
import time
from datetime import datetime


#logger = logger_config.setup_logger('main_logger', 'main.log')

sides = {
    "CE_SELL": "SELL",
    "PE_SELL": "SELL",
    "CE_BUY": "BUY",
    "PE_BUY": "BUY"
}

def thursday_execute_BCS(mainkey, subfolders, start_time, exit_time, folder_date_dt,stop_loss, target_profit, ratio_check, resize_factor):
       
    target_date = os.path.basename(mainkey)
    #logger.info(f"Target Date: {target_date}")
    
    vix_close_value = get_vix(target_date,start_time)
    if vix_close_value is None:
        #logger.info(f"Skipping processing for {target_date} due to missing VIX data.")
        return None

    sell_option_price = get_sell_option_price(vix_close_value)
    #logger.info(f"sell_option_price: {sell_option_price}")
    calender_option_price = get_calender_diff_value(sell_option_price, vix_close_value)
    #logger.info(f"calender_option_price: {calender_option_price}")

    resize = resize_factor
    sell_option_price *= resize
    calender_option_price *= resize

   
   
    results = {}

    def process_option(subfolder, side, target_price):
        try:
            if subfolder not in subfolders:
                raise ValueError(f"{subfolder} not found in subfolders dict.")

            # subfolders[subfolder] is now a dict of {filename: DataFrame}
            # We need to combine all DataFrames for this subfolder
            subfolder_data = subfolders[subfolder]
            if not subfolder_data:
                raise ValueError(f"No data found for {subfolder}")

            # Combine all DataFrames for this subfolder
            dfs_to_combine = []
            for filename, df in subfolder_data.items():
                # Add filename as a column to track which file each row came from
                df_copy = df.copy()
                df_copy['source_file'] = filename
                dfs_to_combine.append(df_copy)

            if not dfs_to_combine:
                raise ValueError(f"No DataFrames found for {subfolder}")

            combined_df = pd.concat(dfs_to_combine, ignore_index=True)
            #logger.info(f"Combined {len(dfs_to_combine)} DataFrames for {subfolder}, total rows: {len(combined_df)}")

            # For the symbol parameter, we'll use the subfolder name as a placeholder
            # The actual symbol will be determined by find_closest_option
            result = find_closest_option(combined_df, target_date, target_price, start_time, subfolder)

            if result is None:
                raise TypeError("find_closest_option returned None")

            symbol, row = result

            #logger.info(f"Found option in {subfolder}: {symbol}")

            if row is not None:
                return subfolder, {
                    "Date": int(row["YMD"]),
                    "Time": row["Time"],
                    "symbol": symbol,
                    "ltp": float(row["Close"]),
                    "side": side
                }

            return subfolder, None

        except Exception as e:
            #logger.exception(f"Error in process_option for {subfolder}: {e}")
            raise


    # 🔧 Dynamically set max_workers based on CPU
    cpu_count = os.cpu_count()
    max_workers = max(1, cpu_count - 1)
    #logger.info(f"Using {max_workers} threads for processing.")
    
    #with ThreadPoolExecutor(max_workers=max_workers) as executor:
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for subfolder, side in sides.items():
            #logger.info(f"Processing {subfolder}...")
            price = sell_option_price if side == "SELL" else calender_option_price
            futures.append(executor.submit(process_option, subfolder, side, price))

        for f in futures:
            #try:
            subfolder, result = f.result()
            if result:
                results[subfolder] = result
            #except Exception as e:
                #logger.exception(f"Exception in thread: {e}")        
    
            
    return results, vix_close_value
    

    
     
